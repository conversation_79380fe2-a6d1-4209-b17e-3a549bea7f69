package space.lzhq.ph.service

import com.ruoyi.system.service.ISysConfigService
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.MockitoAnnotations
import org.springframework.web.client.RestClientException
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.UriComponentsBuilder
import java.net.URI

class RadOnlineServiceTest {

    @Mock
    private lateinit var sysConfigService: ISysConfigService

    @Mock
    private lateinit var restTemplate: RestTemplate
    
    @Mock
    private lateinit var encryptionService: MobileImagingEncryptionService
    
    private lateinit var radOnlineService: RadOnlineService

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        radOnlineService = RadOnlineService(sysConfigService, restTemplate, encryptionService)
    }

    @Test
    fun `getToken should return null when username config is missing`() {
        // Given
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.username")).thenReturn(null)

        // When
        val result = radOnlineService.getToken()

        // Then
        assertNull(result, "缺失用户名配置时应返回null")
    }

    @Test
    fun `getToken should return null when password config is missing`() {
        // Given
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.username")).thenReturn("user")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.password")).thenReturn(null)

        // When
        val result = radOnlineService.getToken()

        // Then
        assertNull(result, "缺失密码配置时应返回null")
    }

    @Test
    fun `getToken should return null when token-url config is missing`() {
        // Given
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.username")).thenReturn("user")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.password")).thenReturn("pass")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.token-url")).thenReturn(null)

        // When
        val result = radOnlineService.getToken()

        // Then
        assertNull(result, "缺失token-url配置时应返回null")
    }

    @Test
    fun `getToken should return token on successful API call`() {
        // Given
        val expectedToken = "test-token-123"
        val url = "https://api.example.com/token"
        val successResponse = RadOnlineTokenResponse(
            success = true,
            data = expectedToken,
            msg = "OK",
            subCode = "SUCCESS",
            subMsg = "OK",
            code = "SUCCESS"
        )

        `when`(sysConfigService.selectConfigByKey("mobile-imaging.username")).thenReturn("testuser")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.password")).thenReturn("testpass")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.token-url")).thenReturn(url)
        `when`(restTemplate.postForObject(eq(url), any(), eq(RadOnlineTokenResponse::class.java)))
            .thenReturn(successResponse)

        // When
        val result = radOnlineService.getToken()

        // Then
        assertEquals(expectedToken, result, "成功响应时应返回正确的token")

        // Verify the correct request was made
        val expectedRequest = mapOf("username" to "testuser", "password" to "testpass")
        verify(restTemplate).postForObject(url, expectedRequest, RadOnlineTokenResponse::class.java)
    }

    @Test
    fun `getToken should return null when API returns failure response`() {
        // Given
        val url = "https://api.example.com/token"
        val failureResponse = RadOnlineTokenResponse(
            success = false,
            data = null,
            msg = "Authentication failed",
            subCode = "AUTH_FAILED",
            subMsg = "Authentication failed",
            code = "AUTH_FAILED"
        )

        `when`(sysConfigService.selectConfigByKey("mobile-imaging.username")).thenReturn("testuser")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.password")).thenReturn("testpass")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.token-url")).thenReturn(url)
        `when`(restTemplate.postForObject(eq(url), any(), eq(RadOnlineTokenResponse::class.java)))
            .thenReturn(failureResponse)

        // When
        val result = radOnlineService.getToken()

        // Then
        assertNull(result, "API返回失败响应时应返回null")

        // Verify the correct request was made
        val expectedRequest = mapOf("username" to "testuser", "password" to "testpass")
        verify(restTemplate).postForObject(url, expectedRequest, RadOnlineTokenResponse::class.java)
    }

    @Test
    fun `getToken should return null on network error`() {
        // Given
        val url = "https://api.example.com/token"

        `when`(sysConfigService.selectConfigByKey("mobile-imaging.username")).thenReturn("testuser")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.password")).thenReturn("testpass")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.token-url")).thenReturn(url)
        `when`(restTemplate.postForObject(eq(url), any(), eq(RadOnlineTokenResponse::class.java)))
            .thenThrow(RestClientException("Network error"))

        // When
        val result = radOnlineService.getToken()

        // Then
        assertNull(result, "网络错误时应返回null")
    }
    
    @Test
    fun `getMobileImagingUrl should return null when patientId is blank`() {
        // When
        val result = radOnlineService.getMobileImagingUrl("   ")

        // Then
        assertNull(result, "患者ID为空白时应返回null")
    }

    @Test
    fun `getMobileImagingUrl should return null when getToken fails`() {
        // Given
        val patientId = "patient123"
        
        // Mock token acquisition failure
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.username")).thenReturn(null)

        // When
        val result = radOnlineService.getMobileImagingUrl(patientId)

        // Then
        assertNull(result, "获取token失败时应返回null")
    }

    @Test
    fun `getMobileImagingUrl should return null when encryption fails`() {
        // Given
        val patientId = "patient123"
        val token = "valid-token"
        
        // Mock successful token acquisition
        mockSuccessfulTokenAcquisition(token)
        
        // Mock encryption failure
        `when`(encryptionService.encryptExamId(patientId))
            .thenThrow(RuntimeException("Encryption failed"))

        // When
        val result = radOnlineService.getMobileImagingUrl(patientId)

        // Then
        assertNull(result, "加密失败时应返回null")
    }

    @Test
    fun `getMobileImagingUrl should return null when URL building fails`() {
        // Given
        val patientId = "patient123"
        val token = "valid-token"
        val encryptedId = "encrypted-patient-123"
        
        // Mock successful token acquisition
        mockSuccessfulTokenAcquisition(token)
        
        // Mock successful encryption
        `when`(encryptionService.encryptExamId(patientId)).thenReturn(encryptedId)
        
        // Mock URL building failure (missing config)
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.unit-id")).thenReturn(null)
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.base-url")).thenReturn("https://example.com")

        // When
        val result = radOnlineService.getMobileImagingUrl(patientId)

        // Then
        assertNull(result, "URL构建失败时应返回null")
    }

    @Test
    fun `getMobileImagingUrl should return complete URL on success`() {
        // Given
        val patientId = "patient123"
        val token = "test-token"
        val encryptedId = "encrypted-patient-123"
        val unitId = "106"
        val baseUrl = "https://film.radonline.cn/web/fore-end/patient.html"
        
        // Mock successful token acquisition
        mockSuccessfulTokenAcquisition(token)
        
        // Mock successful encryption
        `when`(encryptionService.encryptExamId(patientId)).thenReturn(encryptedId)
        
        // Mock URL building configuration
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.unit-id")).thenReturn(unitId)
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.base-url")).thenReturn(baseUrl)

        // When
        val result = radOnlineService.getMobileImagingUrl(patientId)

        // Then
        val uri = URI(result!!)
        val queryParams = UriComponentsBuilder.fromUri(uri).build().queryParams

        assertAll(
            "URL components",
            { assertEquals("https", uri.scheme) },
            { assertEquals("film.radonline.cn", uri.host) },
            { assertEquals("/web/fore-end/patient.html", uri.path) },
            { assertEquals("/check-detail", uri.fragment) },
            { assertEquals("test-token", queryParams.getFirst("token")) },
            { assertEquals("106", queryParams.getFirst("unitid")) },
            { assertEquals("encrypted-patient-123", queryParams.getFirst("xeguid")) },
            { assertEquals("true", queryParams.getFirst("hasList")) },
            { assertEquals("true", queryParams.getFirst("callBack")) }
        )
        
        // Verify the correct methods were called
        verify(encryptionService).encryptExamId(patientId)
    }

    // ========== Helper Methods ==========

    private fun mockSuccessfulTokenAcquisition(token: String) {
        val url = "https://api.example.com/token"
        val successResponse = RadOnlineTokenResponse(
            success = true,
            data = token,
            msg = "OK",
            subCode = "SUCCESS",
            subMsg = "OK",
            code = "SUCCESS"
        )

        `when`(sysConfigService.selectConfigByKey("mobile-imaging.username")).thenReturn("testuser")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.password")).thenReturn("testpass")
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.token-url")).thenReturn(url)
        `when`(restTemplate.postForObject(eq(url), any(), eq(RadOnlineTokenResponse::class.java)))
            .thenReturn(successResponse)
    }
}