# Implementation Plan

- [x] 1. Implement RSA encryption in MobileImagingEncryptionService using standard library
  - Use Java standard crypto APIs (javax.crypto.Cipher, java.security.KeyFactory) directly
  - Implement encryptExamId() method with fail-fast error handling using require()
  - Integrate with ISysConfigService for RSA public key configuration
  - Use clean API without nullable returns - throw exceptions for invalid input
  - Add appropriate logging using existing logger patterns
  - Removed custom RSAUtil class - use standard library instead
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 6.4, 6.5_

- [x] 2. Create RadOnline service for token management
  - Create simple RadOnlineService class with @Service annotation
  - Implement getToken() method returning String? (null on failure)
  - Use RestTemplate for direct HTTP calls
  - Simple configuration loading and error handling
  - Write focused unit tests covering key scenarios
  - _Requirements: 1.1, 2.1, 4.1, 4.2, 4.3, 4.4, 6.2, 6.4_

- [x] 3. Integrate complete URL generation into RadOnlineService
  - Add getMobileImagingUrl(patientId) method - one-stop service for Controller layer
  - Integrate MobileImagingEncryptionService for patient ID encryption
  - Add internal buildMobileImagingUrl() method using Spring's UriComponentsBuilder
  - Integrate with system configuration for hospital unit ID and base URL
  - Simple null-based error handling consistent with getToken() method
  - Remove redundant MobileImagingUrlBuilder class (over-engineered)
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 6.5_

- [x] 4. Add mobile imaging endpoint to MenzhenController
  - Add getMobileImagingUrl() method with proper annotations
  - Use @RequireActivePatient(shouldBeMenzhenPatient = true) annotation
  - Validate patient ID card number using existing IdCardUtil
  - Single call to RadOnlineService.getMobileImagingUrl(patientId) - simplified integration
  - Return AjaxResult following existing controller patterns
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 6.1, 6.2_

- [x] 5. Add mobile imaging endpoint to ZhuyuanPatientApiController
  - Add getMobileImagingUrl() method with proper annotations
  - Use @RequireActivePatient(shouldBeZhuyuanPatient = true) annotation
  - Validate patient admission status and information
  - Single call to RadOnlineService.getMobileImagingUrl(patientId) - simplified integration
  - Return AjaxResult following existing controller patterns
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 6.1, 6.2_

- [ ] 6. Add system configuration support
  - Add configuration keys for mobile imaging feature
  - Configure RadOnline API credentials and endpoints (mobile-imaging.token-url, username, password)
  - Set up RSA public key configuration (mobile-imaging.public-key)
  - Add feature toggle configuration (mobile-imaging.enabled)
  - Configure hospital unit ID parameter (mobile-imaging.unit-id) - required
  - Configure RadOnline base URL (mobile-imaging.base-url) - required
  - _Requirements: 4.4, 5.3, 6.3_

- [ ] 7. Implement comprehensive error handling
  - Add validation for all input parameters
  - Handle RadOnline API authentication failures
  - Handle RSA encryption errors gracefully
  - Implement proper logging for all error scenarios
  - Return user-friendly error messages via AjaxResult
  - _Requirements: 1.5, 1.6, 2.5, 2.6, 3.5, 4.4, 6.2, 6.4_

- [ ] 8. Write integration tests for complete workflow
  - Test end-to-end mobile imaging URL generation for outpatients
  - Test end-to-end mobile imaging URL generation for inpatients
  - Test error scenarios and edge cases
  - Verify security annotations and patient validation
  - Test configuration loading and validation
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 2.3, 2.4, 6.1_

- [ ] 9. Add performance optimizations and monitoring
  - Implement async processing using existing coroutine patterns
  - Add appropriate caching for tokens if needed
  - Configure HTTP client timeouts and retry logic
  - Add logging for monitoring and debugging
  - _Requirements: 4.1, 4.2, 4.3, 6.4_

- [ ] 10. Update documentation and configuration examples
  - Document new configuration keys and their purposes
  - Provide example RSA key configuration
  - Document API endpoints and their usage
  - Add troubleshooting guide for common issues
  - _Requirements: 3.3, 4.4, 5.5_